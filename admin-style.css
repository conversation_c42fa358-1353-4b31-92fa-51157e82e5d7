/* admin-style.css - تنسيقات قسم إدارة المشرفين */

/* ===== قسم إدارة المشرفين ===== */
#adminSection {
    padding: 20px;
}

#adminSection h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.admin-panel {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 30px;
}

.admin-form {
    flex: 1;
    min-width: 300px;
    background-color: #fff;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.admin-form h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
}

.admin-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: 'Ta<PERSON>wal', sans-serif;
    font-weight: 500;
    margin-top: 10px;
}

.admin-btn:hover {
    background-color: #004494;
    transform: translateY(-2px);
}

.admin-btn:active {
    transform: translateY(0);
}

.admins-list {
    flex: 2;
    min-width: 400px;
    background-color: #fff;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.admins-list h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
}

.admins-table-container {
    overflow-x: auto;
}

.admins-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.admins-table th,
.admins-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #ddd;
}

.admins-table th {
    background-color: #f8f9fa;
    color: var(--dark-color);
    font-weight: 600;
}

.admins-table tr:hover {
    background-color: #f8f9fa;
}

.delete-admin-btn {
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    cursor: pointer;
    transition: var(--transition);
}

.delete-admin-btn:hover {
    background-color: #c82333;
}

.main-admin-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
}

/* ===== معلومات المستخدم في الهيدر ===== */
.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 6px 20px;
    border-radius: 25px;
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.user-info i {
    font-size: 1.1rem;
    color: white;
}

.admin-badge {
    background-color: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-right: 5px;
}

/* ===== زر تسجيل الخروج ===== */
.logout-link {
    color: var(--danger-color) !important;
}

.logout-link:hover {
    color: #c82333 !important;
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 768px) {
    .admin-panel {
        flex-direction: column;
    }

    .user-info {
        font-size: 0.8rem;
        padding: 4px 15px;
    }

    .user-info-bar {
        padding: 6px 0;
        min-height: 35px;
    }
}
