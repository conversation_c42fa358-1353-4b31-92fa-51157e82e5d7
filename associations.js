// associations.js - إدارة جمعيات الزواج المتعددة

// متغيرات عامة
let currentAssociation = null;
let associations = [];

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadAssociations();
    renderAssociations();
    setupEventListeners();
    
    // إضافة مسؤول افتراضي عند فتح النموذج
    setTimeout(() => {
        addManagerField();
    }, 100);
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    const addAssociationBtn = document.getElementById('addAssociationBtn');
    if (addAssociationBtn) {
        addAssociationBtn.addEventListener('click', openAddAssociationModal);
    }

    // تعيين تاريخ اليوم كافتراضي
    const creationDateInput = document.getElementById('creationDate');
    if (creationDateInput) {
        creationDateInput.value = new Date().toISOString().split('T')[0];
    }
}

// فتح نموذج إضافة جمعية جديدة
function openAddAssociationModal() {
    const modal = document.getElementById('addAssociationModal');
    if (modal) {
        modal.style.display = 'block';
        
        // إعادة تعيين النموذج
        document.getElementById('addAssociationForm').reset();
        document.getElementById('creationDate').value = new Date().toISOString().split('T')[0];
        
        // مسح المسؤولين وإضافة حقل واحد
        const managersContainer = document.getElementById('managersContainer');
        managersContainer.innerHTML = '';
        addManagerField();
    }
}

// إغلاق نموذج إضافة جمعية
function closeAddAssociationModal() {
    const modal = document.getElementById('addAssociationModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// إضافة حقل مسؤول جديد
function addManagerField() {
    const managersContainer = document.getElementById('managersContainer');
    const managerIndex = managersContainer.children.length;
    
    const managerDiv = document.createElement('div');
    managerDiv.className = 'manager-field';
    managerDiv.innerHTML = `
        <div class="manager-header">
            <h5><i class="fas fa-user-tie"></i> مسؤول ${managerIndex + 1}</h5>
            ${managerIndex > 0 ? '<button type="button" class="remove-manager-btn" onclick="removeManagerField(this)"><i class="fas fa-times"></i></button>' : ''}
        </div>
        <div class="form-row">
            <div class="form-group">
                <label>
                    <i class="fas fa-user"></i>
                    الاسم الكامل
                </label>
                <input type="text" name="managerName_${managerIndex}" placeholder="الاسم الكامل للمسؤول" required>
            </div>
            <div class="form-group">
                <label>
                    <i class="fas fa-mobile-alt"></i>
                    رقم الجوال
                </label>
                <input type="tel" name="managerPhone_${managerIndex}" placeholder="05xxxxxxxx" 
                       pattern="05\\d{8}" title="يجب أن يبدأ الرقم بـ 05 ويتكون من 10 أرقام" required>
            </div>
        </div>
        <div class="form-group">
            <label>
                <i class="fas fa-id-card"></i>
                رقم الهوية الوطنية
            </label>
            <input type="text" name="managerNationalId_${managerIndex}" placeholder="رقم الهوية (10 أرقام)" 
                   pattern="\\d{10}" maxlength="10" title="يجب أن يتكون رقم الهوية من 10 أرقام فقط" required>
        </div>
    `;
    
    managersContainer.appendChild(managerDiv);
}

// حذف حقل مسؤول
function removeManagerField(button) {
    const managerField = button.closest('.manager-field');
    if (managerField) {
        managerField.remove();
        
        // إعادة ترقيم المسؤولين
        const managersContainer = document.getElementById('managersContainer');
        const managerFields = managersContainer.querySelectorAll('.manager-field');
        managerFields.forEach((field, index) => {
            const header = field.querySelector('.manager-header h5');
            header.innerHTML = `<i class="fas fa-user-tie"></i> مسؤول ${index + 1}`;
        });
    }
}

// حفظ الجمعية الجديدة
function saveAssociation() {
    const form = document.getElementById('addAssociationForm');
    const formData = new FormData(form);
    
    // التحقق من صحة البيانات
    if (!validateAssociationForm(formData)) {
        return;
    }
    
    // إنشاء كائن الجمعية الجديدة
    const newAssociation = {
        id: generateAssociationId(),
        name: formData.get('associationName'),
        creationDate: formData.get('creationDate'),
        amount: parseInt(formData.get('associationAmount')),
        managers: extractManagers(formData),
        createdAt: new Date().toISOString(),
        membersCount: 0,
        totalCollected: 0,
        isActive: true
    };
    
    // إضافة الجمعية إلى القائمة
    associations.push(newAssociation);
    
    // حفظ في التخزين المحلي
    saveAssociations();
    
    // إعادة عرض الجمعيات
    renderAssociations();
    
    // إغلاق النموذج
    closeAddAssociationModal();
    
    // عرض رسالة نجاح
    showSuccessMessage('تم إنشاء الجمعية بنجاح!');
}

// التحقق من صحة نموذج الجمعية
function validateAssociationForm(formData) {
    const associationName = formData.get('associationName');
    const creationDate = formData.get('creationDate');
    const amount = formData.get('associationAmount');
    
    if (!associationName || associationName.trim().length < 3) {
        showErrorMessage('يجب إدخال اسم الجمعية (3 أحرف على الأقل)');
        return false;
    }
    
    if (!creationDate) {
        showErrorMessage('يجب تحديد تاريخ الإنشاء');
        return false;
    }
    
    if (!amount || amount < 1) {
        showErrorMessage('يجب إدخال مبلغ صحيح للجمعية');
        return false;
    }
    
    // التحقق من وجود مسؤول واحد على الأقل
    const managersContainer = document.getElementById('managersContainer');
    const managerFields = managersContainer.querySelectorAll('.manager-field');
    
    if (managerFields.length === 0) {
        showErrorMessage('يجب إضافة مسؤول واحد على الأقل');
        return false;
    }
    
    // التحقق من بيانات المسؤولين
    for (let i = 0; i < managerFields.length; i++) {
        const managerName = formData.get(`managerName_${i}`);
        const managerPhone = formData.get(`managerPhone_${i}`);
        const managerNationalId = formData.get(`managerNationalId_${i}`);
        
        if (!managerName || managerName.trim().length < 3) {
            showErrorMessage(`يجب إدخال اسم المسؤول ${i + 1}`);
            return false;
        }
        
        if (!managerPhone || !/^05\d{8}$/.test(managerPhone)) {
            showErrorMessage(`رقم جوال المسؤول ${i + 1} غير صحيح`);
            return false;
        }
        
        if (!managerNationalId || !/^\d{10}$/.test(managerNationalId)) {
            showErrorMessage(`رقم هوية المسؤول ${i + 1} يجب أن يتكون من 10 أرقام`);
            return false;
        }
    }
    
    return true;
}

// استخراج بيانات المسؤولين من النموذج
function extractManagers(formData) {
    const managers = [];
    const managersContainer = document.getElementById('managersContainer');
    const managerFields = managersContainer.querySelectorAll('.manager-field');
    
    managerFields.forEach((field, index) => {
        const manager = {
            id: generateManagerId(),
            name: formData.get(`managerName_${index}`),
            phone: formData.get(`managerPhone_${index}`),
            nationalId: formData.get(`managerNationalId_${index}`),
            role: index === 0 ? 'رئيس' : 'عضو'
        };
        managers.push(manager);
    });
    
    return managers;
}

// إنشاء معرف فريد للجمعية
function generateAssociationId() {
    return 'assoc_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// إنشاء معرف فريد للمسؤول
function generateManagerId() {
    return 'mgr_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// تحميل الجمعيات من التخزين المحلي
function loadAssociations() {
    const savedAssociations = localStorage.getItem('associations');
    if (savedAssociations) {
        associations = JSON.parse(savedAssociations);
    }
    
    // تحديد الجمعية الحالية
    const currentAssocId = localStorage.getItem('currentAssociation');
    if (currentAssocId && associations.length > 0) {
        currentAssociation = associations.find(a => a.id === currentAssocId) || associations[0];
    } else if (associations.length > 0) {
        currentAssociation = associations[0];
    }
}

// حفظ الجمعيات في التخزين المحلي
function saveAssociations() {
    localStorage.setItem('associations', JSON.stringify(associations));
    if (currentAssociation) {
        localStorage.setItem('currentAssociation', currentAssociation.id);
    }
}

// عرض الجمعيات في الصفحة الرئيسية
function renderAssociations() {
    const container = document.getElementById('associationsContainer');
    const emptyState = document.getElementById('emptyState');

    if (!container) return;

    if (associations.length === 0) {
        emptyState.style.display = 'block';
        return;
    }

    emptyState.style.display = 'none';

    // مسح المحتوى السابق
    const existingCards = container.querySelectorAll('.association-card');
    existingCards.forEach(card => card.remove());

    // إنشاء بطاقة لكل جمعية
    associations.forEach(association => {
        const card = createAssociationCard(association);
        container.appendChild(card);
    });
}

// إنشاء بطاقة جمعية
function createAssociationCard(association) {
    const card = document.createElement('div');
    card.className = `association-card ${currentAssociation && currentAssociation.id === association.id ? 'active' : ''}`;
    card.setAttribute('data-association-id', association.id);

    // حساب الإحصائيات
    const membersCount = getAssociationMembersCount(association.id);
    const totalCollected = getAssociationTotalCollected(association.id);
    const activeMembers = getAssociationActiveMembers(association.id);

    card.innerHTML = `
        <div class="card-header">
            <div class="card-title">
                <h3><i class="fas fa-heart"></i> ${association.name}</h3>
                <div class="card-status ${association.isActive ? 'active' : 'inactive'}">
                    ${association.isActive ? 'نشطة' : 'غير نشطة'}
                </div>
            </div>
            <div class="card-actions">
                <button class="action-btn edit-btn" onclick="editAssociation('${association.id}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn delete-btn" onclick="deleteAssociation('${association.id}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>

        <div class="card-body">
            <div class="card-info">
                <div class="info-item">
                    <i class="fas fa-calendar-alt"></i>
                    <span>تاريخ الإنشاء: ${formatDate(association.creationDate)}</span>
                </div>
                <div class="info-item">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>مبلغ الجمعية: ${association.amount.toLocaleString()} ريال</span>
                </div>
                <div class="info-item">
                    <i class="fas fa-users-cog"></i>
                    <span>عدد المسؤولين: ${association.managers.length}</span>
                </div>
            </div>

            <div class="card-stats">
                <div class="stat-item">
                    <div class="stat-value">${membersCount}</div>
                    <div class="stat-label">إجمالي الأعضاء</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${activeMembers}</div>
                    <div class="stat-label">الأعضاء النشطون</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${totalCollected.toLocaleString()}</div>
                    <div class="stat-label">إجمالي التحصيل</div>
                </div>
            </div>

            <div class="managers-preview">
                <h4><i class="fas fa-user-tie"></i> المسؤولون:</h4>
                <div class="managers-list">
                    ${association.managers.map(manager => `
                        <div class="manager-item">
                            <span class="manager-name">${manager.name}</span>
                            <span class="manager-role">(${manager.role})</span>
                            <span class="manager-phone">${manager.phone}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>

        <div class="card-footer">
            <button class="select-btn ${currentAssociation && currentAssociation.id === association.id ? 'selected' : ''}"
                    onclick="selectAssociation('${association.id}')">
                <i class="fas fa-check-circle"></i>
                ${currentAssociation && currentAssociation.id === association.id ? 'الجمعية المحددة' : 'تحديد كجمعية حالية'}
            </button>
        </div>
    `;

    return card;
}

// تحديد جمعية كجمعية حالية
function selectAssociation(associationId) {
    const association = associations.find(a => a.id === associationId);
    if (!association) return;

    currentAssociation = association;
    saveAssociations();

    // إعادة عرض الجمعيات لتحديث الحالة
    renderAssociations();

    // تحديث البيانات في باقي الأقسام
    updateCurrentAssociationData();

    showSuccessMessage(`تم تحديد "${association.name}" كجمعية حالية`);
}

// تحديث بيانات الجمعية الحالية في باقي الأقسام
function updateCurrentAssociationData() {
    // إعادة تحميل بيانات الأعضاء للجمعية الحالية
    if (typeof loadMembers === 'function') {
        loadMembers();
    }

    // إعادة تحميل بيانات التحصيل
    if (typeof loadCollections === 'function') {
        loadCollections();
    }

    // تحديث عنوان الصفحة أو أي عناصر أخرى حسب الحاجة
    updatePageTitle();
}

// تحديث عنوان الصفحة
function updatePageTitle() {
    if (currentAssociation) {
        document.title = `${currentAssociation.name} - جمعية زواج`;
    }
}

// حساب عدد أعضاء الجمعية
function getAssociationMembersCount(associationId) {
    const members = getAssociationMembers(associationId);
    return members.length;
}

// حساب إجمالي التحصيل للجمعية
function getAssociationTotalCollected(associationId) {
    const collections = getAssociationCollections(associationId);
    return collections.reduce((total, collection) => total + collection.amount, 0);
}

// حساب عدد الأعضاء النشطين
function getAssociationActiveMembers(associationId) {
    const members = getAssociationMembers(associationId);
    return members.filter(member => member.status === 'نشط').length;
}

// الحصول على أعضاء جمعية معينة
function getAssociationMembers(associationId) {
    const allMembers = JSON.parse(localStorage.getItem('members') || '[]');
    return allMembers.filter(member => member.associationId === associationId);
}

// الحصول على تحصيلات جمعية معينة
function getAssociationCollections(associationId) {
    const allCollections = JSON.parse(localStorage.getItem('collections') || '[]');
    return allCollections.filter(collection => collection.associationId === associationId);
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
    // يمكن تحسين هذا لاحقاً بإضافة نظام إشعارات أفضل
    alert(message);
}

// عرض رسالة خطأ
function showErrorMessage(message) {
    // يمكن تحسين هذا لاحقاً بإضافة نظام إشعارات أفضل
    alert(message);
}

// تعديل جمعية
function editAssociation(associationId) {
    // سيتم تطوير هذه الوظيفة لاحقاً
    showSuccessMessage('ميزة التعديل ستكون متاحة قريباً');
}

// حذف جمعية
function deleteAssociation(associationId) {
    const association = associations.find(a => a.id === associationId);
    if (!association) return;

    if (confirm(`هل أنت متأكد من حذف جمعية "${association.name}"؟\nسيتم حذف جميع البيانات المرتبطة بها.`)) {
        // حذف الجمعية من القائمة
        associations = associations.filter(a => a.id !== associationId);

        // إذا كانت الجمعية المحذوفة هي الحالية، تحديد جمعية أخرى
        if (currentAssociation && currentAssociation.id === associationId) {
            currentAssociation = associations.length > 0 ? associations[0] : null;
        }

        // حذف البيانات المرتبطة بالجمعية
        deleteAssociationData(associationId);

        // حفظ التغييرات
        saveAssociations();

        // إعادة عرض الجمعيات
        renderAssociations();

        showSuccessMessage('تم حذف الجمعية بنجاح');
    }
}

// حذف البيانات المرتبطة بالجمعية
function deleteAssociationData(associationId) {
    // حذف الأعضاء
    const members = JSON.parse(localStorage.getItem('members') || '[]');
    const filteredMembers = members.filter(member => member.associationId !== associationId);
    localStorage.setItem('members', JSON.stringify(filteredMembers));

    // حذف التحصيلات
    const collections = JSON.parse(localStorage.getItem('collections') || '[]');
    const filteredCollections = collections.filter(collection => collection.associationId !== associationId);
    localStorage.setItem('collections', JSON.stringify(filteredCollections));
}
