/**
 * auth-system.js
 * نظام المصادقة وإدارة المستخدمين
 */

// تهيئة نظام المصادقة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من حالة تسجيل الدخول
    checkLoginStatus();

    // إضافة زر تسجيل الخروج إلى القائمة
    addLogoutButton();

    // تطبيق الصلاحيات على واجهة المستخدم
    applyPermissions();
});

// وظيفة للتحقق من حالة تسجيل الدخول
function checkLoginStatus() {
    const currentUser = getCurrentUser();

    // إذا لم يكن هناك مستخدم مسجل الدخول، توجيهه إلى صفحة تسجيل الدخول
    if (!currentUser) {
        window.location.href = 'login.html';
        return;
    }

    // إضافة معلومات المستخدم إلى الصفحة
    addUserInfoToHeader(currentUser);

    // تسجيل عملية تسجيل الدخول في السجل إذا لم يتم تسجيلها من قبل
    const lastLoginLog = getLastLoginLog();
    if (!lastLoginLog || !isSameDay(new Date(lastLoginLog.timestamp), new Date())) {
        addActionLog('login', 'تسجيل دخول إلى النظام');
    }
}

// وظيفة للحصول على المستخدم الحالي
function getCurrentUser() {
    return JSON.parse(localStorage.getItem('currentUser'));
}

// وظيفة للحصول على آخر سجل لتسجيل الدخول
function getLastLoginLog() {
    const logs = getActionLogs();
    const loginLogs = logs.filter(log =>
        log.action === 'login' &&
        log.userId === getCurrentUser().id
    );

    if (loginLogs.length === 0) return null;

    // ترتيب السجلات حسب التاريخ (الأحدث أولاً)
    loginLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return loginLogs[0];
}

// وظيفة للتحقق مما إذا كان تاريخان في نفس اليوم
function isSameDay(date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
}

// وظيفة لإضافة معلومات المستخدم إلى الهيدر
function addUserInfoToHeader(user) {
    // إزالة معلومات المستخدم السابقة إن وجدت
    const existingUserInfo = document.querySelector('.user-info');
    if (existingUserInfo) {
        existingUserInfo.remove();
    }

    // إنشاء عنصر معلومات المستخدم
    const userInfoElement = document.createElement('div');
    userInfoElement.className = 'user-info';

    // إضافة محتوى معلومات المستخدم
    userInfoElement.innerHTML = `
        <div class="user-avatar">
            <i class="fas fa-user-circle"></i>
        </div>
        <div class="user-details">
            <span class="user-name">مرحباً، ${user.name}</span>
            ${user.isAdmin ? '<span class="admin-badge">مشرف</span>' : '<span class="user-badge">مستخدم</span>'}
        </div>
    `;

    // إضافة معلومات المستخدم إلى الهيدر
    const header = document.querySelector('header');
    if (header) {
        header.appendChild(userInfoElement);
    }
}

// وظيفة لإضافة زر تسجيل الخروج إلى القائمة
function addLogoutButton() {
    const navList = document.querySelector('.main-nav ul');
    if (!navList) return;

    // التحقق من عدم وجود زر تسجيل الخروج بالفعل
    if (document.querySelector('.logout-item')) return;

    // إنشاء عنصر زر تسجيل الخروج
    const logoutItem = document.createElement('li');
    logoutItem.className = 'nav-item logout-item';

    // إضافة رابط تسجيل الخروج
    const logoutLink = document.createElement('a');
    logoutLink.href = '#';
    logoutLink.className = 'nav-link logout-link';
    logoutLink.innerHTML = '<i class="fas fa-sign-out-alt"></i> تسجيل الخروج';
    logoutLink.addEventListener('click', function(e) {
        e.preventDefault();
        logout();
    });

    // إضافة الرابط إلى العنصر
    logoutItem.appendChild(logoutLink);

    // إضافة العنصر إلى القائمة
    navList.appendChild(logoutItem);
}

// وظيفة لتسجيل الخروج
function logout() {
    // عرض تأكيد قبل تسجيل الخروج
    if (confirm('هل أنت متأكد من رغبتك في تسجيل الخروج؟')) {
        // تسجيل عملية تسجيل الخروج في السجل
        addActionLog('logout', 'تسجيل خروج من النظام');

        // حذف بيانات المستخدم من التخزين المحلي
        localStorage.removeItem('currentUser');

        // توجيه المستخدم إلى صفحة تسجيل الدخول
        window.location.href = 'login.html';
    }
}

// وظيفة لتطبيق الصلاحيات على واجهة المستخدم
function applyPermissions() {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    // إذا كان المستخدم ليس مشرفًا، إخفاء أزرار التعديل والحذف
    if (!currentUser.isAdmin) {
        // إخفاء أزرار التعديل والحذف في جدول الأعضاء
        hideEditDeleteButtons();

        // إخفاء أزرار التعديل والحذف في جدول التحصيلات
        hideCollectionEditButtons();

        // إخفاء قسم إدارة المشرفين
        hideAdminSection();
    } else {
        // إظهار قسم إدارة المشرفين للمشرفين فقط
        showAdminSection();
    }
}

// وظيفة لإخفاء أزرار التعديل والحذف في جدول الأعضاء
function hideEditDeleteButtons() {
    // استخدام MutationObserver لمراقبة التغييرات في DOM
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                // البحث عن أزرار التعديل والحذف وإخفائها
                const editButtons = document.querySelectorAll('.edit-btn, .delete-btn');
                editButtons.forEach(button => {
                    button.style.display = 'none';
                });
            }
        });
    });

    // بدء مراقبة جدول الأعضاء
    const membersTable = document.getElementById('membersBody');
    if (membersTable) {
        observer.observe(membersTable, { childList: true, subtree: true });

        // إخفاء الأزرار الموجودة بالفعل
        const existingButtons = membersTable.querySelectorAll('.edit-btn, .delete-btn');
        existingButtons.forEach(button => {
            button.style.display = 'none';
        });
    }
}

// وظيفة لإخفاء أزرار التعديل والحذف في جدول التحصيلات
function hideCollectionEditButtons() {
    // استخدام MutationObserver لمراقبة التغييرات في DOM
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                // البحث عن أزرار الحذف وإخفائها
                const deleteButtons = document.querySelectorAll('#collectionBody .delete-btn');
                deleteButtons.forEach(button => {
                    button.style.display = 'none';
                });
            }
        });
    });

    // بدء مراقبة جدول التحصيلات
    const collectionTable = document.getElementById('collectionBody');
    if (collectionTable) {
        observer.observe(collectionTable, { childList: true, subtree: true });

        // إخفاء الأزرار الموجودة بالفعل
        const existingButtons = collectionTable.querySelectorAll('.delete-btn');
        existingButtons.forEach(button => {
            button.style.display = 'none';
        });
    }

    // إخفاء نموذج إضافة تحصيل جديد
    const collectionForm = document.getElementById('collectionForm');
    if (collectionForm) {
        collectionForm.style.display = 'none';
    }
}

// وظيفة لإخفاء قسم إدارة المشرفين
function hideAdminSection() {
    const adminSection = document.getElementById('adminSection');
    if (adminSection) {
        adminSection.style.display = 'none';
    }

    // إخفاء رابط إدارة المشرفين من القائمة
    const adminLink = document.querySelector('a[href="#adminSection"]');
    if (adminLink) {
        const adminItem = adminLink.closest('.nav-item');
        if (adminItem) {
            adminItem.style.display = 'none';
        }
    }
}

// وظيفة لإضافة سجل إجراء جديد
function addActionLog(action, details, targetId = null) {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    // الحصول على سجلات الإجراءات الحالية
    const logs = getActionLogs();

    // إنشاء سجل جديد
    const newLog = {
        id: generateUniqueId(),
        timestamp: new Date().toISOString(),
        userId: currentUser.id,
        userName: currentUser.name,
        action: action,
        details: details,
        targetId: targetId
    };

    // إضافة السجل الجديد إلى بداية المصفوفة
    logs.unshift(newLog);

    // حفظ السجلات المحدثة
    localStorage.setItem('actionLogs', JSON.stringify(logs));

    return newLog;
}

// وظيفة للحصول على سجلات الإجراءات
function getActionLogs() {
    const logs = localStorage.getItem('actionLogs');
    return logs ? JSON.parse(logs) : [];
}

// وظيفة لإنشاء معرف فريد
function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// تصدير الوظائف للاستخدام في ملفات أخرى
window.authSystem = {
    getCurrentUser,
    addActionLog,
    getActionLogs
};
